# Guía Completa: Configuración de Dominio Personalizado para Mailgun

## ¿Por qué configurar un dominio personalizado?

### Problemas del dominio sandbox:
- ❌ Baja reputación de envío
- ❌ Más probabilidad de ir a spam
- ❌ Limitado a 300 emails/día
- ❌ Solo destinatarios autorizados

### Beneficios del dominio personalizado:
- ✅ Mayor deliverabilidad
- ✅ Mejor reputación de envío
- ✅ Sin límites de destinatarios
- ✅ Autenticación SPF/DKIM/DMARC
- ✅ Apariencia más profesional

## Paso 1: Preparación

### Requisitos:
- Dominio propio (ej: `buscafarma.com`)
- Acceso al panel DNS del dominio
- Cuenta de Mailgun activa

### Subdominios recomendados:
- `mail.buscafarma.com` - Para envío de emails
- `mg.buscafarma.com` - Para tracking de Mailgun

## Paso 2: Configurar Dominio en Mailgun

1. **Acceder a Mailgun Dashboard:**
   - Ve a https://app.mailgun.com/
   - Login con tu cuenta

2. **Agregar nuevo dominio:**
   - Domains → Add New Domain
   - Ingresa: `mail.buscafarma.com`
   - Selecciona región: US o EU
   - Click "Add Domain"

3. **Obtener registros DNS:**
   Mailgun te proporcionará registros similares a estos:

## Paso 3: Configurar Registros DNS

### Registros SPF (TXT):
```
Nombre: mail.buscafarma.com
Tipo: TXT
Valor: v=spf1 include:mailgun.org ~all
TTL: 300
```

### Registros DKIM (TXT):
```
Nombre: pic._domainkey.mail.buscafarma.com
Tipo: TXT
Valor: k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC... (clave larga)
TTL: 300
```

### Registros MX:
```
Nombre: mail.buscafarma.com
Tipo: MX
Valor: mxa.mailgun.org
Prioridad: 10
TTL: 300

Nombre: mail.buscafarma.com
Tipo: MX
Valor: mxb.mailgun.org
Prioridad: 10
TTL: 300
```

### Registro CNAME para tracking:
```
Nombre: email.mail.buscafarma.com
Tipo: CNAME
Valor: mailgun.org
TTL: 300
```

## Paso 4: Configurar DMARC (Opcional pero recomendado)

### Registro DMARC:
```
Nombre: _dmarc.mail.buscafarma.com
Tipo: TXT
Valor: v=DMARC1; p=none; rua=mailto:<EMAIL>; ruf=mailto:<EMAIL>; sp=none; aspf=r;
TTL: 300
```

## Paso 5: Verificar Configuración

### En Mailgun Dashboard:
1. Ve a tu dominio recién creado
2. Click "Verify DNS Settings"
3. Espera que todos los checks sean ✅

### Comandos de verificación:
```bash
# Verificar SPF
dig TXT mail.buscafarma.com

# Verificar DKIM
dig TXT pic._domainkey.mail.buscafarma.com

# Verificar MX
dig MX mail.buscafarma.com

# Verificar DMARC
dig TXT _dmarc.mail.buscafarma.com
```

## Paso 6: Actualizar Configuración de BuscaFarma

### Actualizar .env:
```bash
# Cambiar de sandbox a dominio personalizado
MAILGUN_DOMAIN=mail.buscafarma.com
EMAIL_FROM=BuscaFarma <<EMAIL>>
```

### Reiniciar aplicación:
```bash
bun run dev
```

## Paso 7: Probar Configuración

### Ejecutar script de prueba:
```bash
bun run src/scripts/test-email.ts <EMAIL>
```

### Verificar logs:
- Buscar respuestas 200 de Mailgun
- Verificar que no hay errores de DNS
- Confirmar que emails llegan a inbox (no spam)

## Paso 8: Monitoreo y Optimización

### Herramientas de verificación:
- https://mxtoolbox.com/spf.aspx
- https://dmarcian.com/dmarc-inspector/
- https://www.mail-tester.com/

### Métricas a monitorear:
- Tasa de entrega
- Tasa de apertura
- Quejas de spam
- Bounces

## Troubleshooting Común

### DNS no propaga:
- Esperar 24-48 horas para propagación completa
- Verificar TTL configurado correctamente
- Usar diferentes servidores DNS para verificar

### Emails siguen yendo a spam:
- Verificar que todos los registros DNS estén correctos
- Revisar contenido de emails (evitar palabras spam)
- Calentar la reputación del dominio gradualmente

### Errores de autenticación:
- Verificar registros SPF y DKIM
- Confirmar que FROM domain coincide con dominio configurado
- Revisar logs de Mailgun para errores específicos

## Configuración Avanzada (Opcional)

### Subdominios dedicados:
- `transactional.buscafarma.com` - Emails transaccionales
- `marketing.buscafarma.com` - Emails de marketing
- `notifications.buscafarma.com` - Notificaciones

### Webhooks para tracking:
```javascript
// Configurar webhooks en Mailgun para tracking avanzado
const webhookConfig = {
  delivered: 'https://api.buscafarma.com/webhooks/mailgun/delivered',
  opened: 'https://api.buscafarma.com/webhooks/mailgun/opened',
  clicked: 'https://api.buscafarma.com/webhooks/mailgun/clicked',
  bounced: 'https://api.buscafarma.com/webhooks/mailgun/bounced',
  complained: 'https://api.buscafarma.com/webhooks/mailgun/complained'
}
```

## Checklist Final

- [ ] Dominio agregado en Mailgun
- [ ] Registros DNS configurados (SPF, DKIM, MX)
- [ ] DMARC configurado
- [ ] DNS verificado en Mailgun
- [ ] Configuración actualizada en .env
- [ ] Aplicación reiniciada
- [ ] Emails de prueba enviados
- [ ] Deliverabilidad verificada
- [ ] Monitoreo configurado

¡Con esta configuración, tus emails tendrán mucha mejor deliverabilidad y menor probabilidad de ir a spam!
