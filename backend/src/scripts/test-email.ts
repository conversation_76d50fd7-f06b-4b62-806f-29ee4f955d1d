#!/usr/bin/env bun

/**
 * Email Testing Script for BuscaFarma Data Deletion System
 *
 * This script tests the email configuration and sends a test email
 * to verify that the Mailgun API setup is working correctly.
 *
 * Usage:
 *   bun run src/scripts/test-email.ts [recipient-email]
 *
 * Environment Variables Required:
 *   MAILGUN_API_KEY - Mailgun API key
 *   MAILGUN_DOMAIN - Verified Mailgun domain
 *   MAILGUN_REGION - Mailgun region (us or eu)
 *   EMAIL_FROM - From email address
 *   ADMIN_EMAIL - Admin email for notifications (optional)
 */

import { EmailService } from '../services/EmailService.js'

async function testEmailConfiguration() {
  console.log('🧪 Testing BuscaFarma Email Configuration...\n')

  // Check environment variables
  const mailgunApiKey = process.env.MAILGUN_API_KEY
  const mailgunDomain = process.env.MAILGUN_DOMAIN
  const mailgunRegion = process.env.MAILGUN_REGION
  const emailFrom = process.env.EMAIL_FROM
  const adminEmail = process.env.ADMIN_EMAIL

  console.log('📋 Environment Configuration:')
  console.log(`   MAILGUN_API_KEY: ${mailgunApiKey ? '✅ Set' : '❌ Missing'}`)
  console.log(`   MAILGUN_DOMAIN: ${mailgunDomain ? '✅ Set' : '❌ Missing'}`)
  console.log(`   MAILGUN_REGION: ${mailgunRegion || 'us (default)'}`)
  console.log(`   EMAIL_FROM: ${emailFrom ? '✅ Set' : '❌ Missing'}`)
  console.log(`   ADMIN_EMAIL: ${adminEmail ? '✅ Set' : '⚠️ Not set (optional)'}`)
  console.log(`   NODE_ENV: ${process.env.NODE_ENV || 'development'}`)
  console.log()

  if (!mailgunApiKey || !mailgunDomain || !emailFrom) {
    console.error('❌ Missing required email configuration!')
    console.error('💡 Please set MAILGUN_API_KEY, MAILGUN_DOMAIN, and EMAIL_FROM environment variables')
    console.error('💡 For Mailgun setup instructions, see EmailService.ts documentation')
    process.exit(1)
  }

  // Create email service configuration
  const emailConfig = {
    apiKey: mailgunApiKey,
    domain: mailgunDomain,
    region: (mailgunRegion as 'us' | 'eu') || 'us',
    from: emailFrom,
    forceEmailSending: true // Force email sending for testing
  }

  const emailService = new EmailService(emailConfig)

  // Test connection
  console.log('🔗 Testing Mailgun API connection...')
  const connectionOk = await emailService.verifyConnection()

  if (!connectionOk) {
    console.error('❌ Email connection failed!')
    console.error('💡 Check your Mailgun API key and domain configuration')
    process.exit(1)
  }

  // Get recipient email from command line or use from email
  const recipientEmail = process.argv[2] || emailFrom
  console.log(`📧 Sending test email to: ${recipientEmail}`)

  // Test deletion confirmation email
  console.log('\n📨 Testing deletion confirmation email...')
  try {
    const confirmationSent = await emailService.sendDeletionRequestConfirmation(recipientEmail, 'Test User', 'test-request-id-123')

    if (confirmationSent) {
      console.log('✅ Deletion confirmation email sent successfully!')
    } else {
      console.log('❌ Deletion confirmation email failed to send')
    }
  } catch (error) {
    console.error('❌ Error sending deletion confirmation email:', error)
  }

  // Test admin notification email (if admin email is configured)
  if (adminEmail) {
    console.log('\n📨 Testing admin notification email...')
    try {
      const adminNotificationSent = await emailService.sendAdminDeletionNotification(adminEmail, recipientEmail, 'test-request-id-123')

      if (adminNotificationSent) {
        console.log('✅ Admin notification email sent successfully!')
      } else {
        console.log('❌ Admin notification email failed to send')
      }
    } catch (error) {
      console.error('❌ Error sending admin notification email:', error)
    }
  } else {
    console.log('\n⚠️ Skipping admin notification test (ADMIN_EMAIL not set)')
  }

  // Test completion notification email
  console.log('\n📨 Testing completion notification email...')
  try {
    const completionSent = await emailService.sendDeletionCompletedNotification(recipientEmail, 'Test User', 'test-request-id-123')

    if (completionSent) {
      console.log('✅ Completion notification email sent successfully!')
    } else {
      console.log('❌ Completion notification email failed to send')
    }
  } catch (error) {
    console.error('❌ Error sending completion notification email:', error)
  }

  console.log('\n🎉 Email testing completed!')
  console.log('💡 Check your inbox for the test emails')
  console.log('💡 If emails are not received, check your spam folder')
}

// Run the test
testEmailConfiguration().catch((error) => {
  console.error('💥 Email test failed:', error)
  process.exit(1)
})
