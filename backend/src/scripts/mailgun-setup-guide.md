# Guía de Configuración de Mailgun para BuscaFarma

## Estado Actual ✅

Los correos se están enviando correctamente a través de Mailgun. El sistema muestra respuestas exitosas:
- Status: 200
- Message: "Queued. Thank you."
- IDs de mensaje generados correctamente

## ¿Por qué no llegan los correos?

### Problema: Dominio Sandbox
Estás usando un dominio sandbox de Mailgun: `sandbox022518cde0b64b308dc70c7306a046d0.mailgun.org`

**Los dominios sandbox tienen limitaciones:**
- Solo pueden enviar correos a direcciones de email **autorizadas**
- Máximo 300 emails por día
- Solo para pruebas y desarrollo

### Solución 1: Autorizar tu dirección de email (Rápido)

1. Ve a tu dashboard de Mailgun: https://app.mailgun.com/
2. Selecciona tu dominio sandbox
3. Ve a la sección "Authorized Recipients"
4. Agrega `<EMAIL>` como destinatario autorizado
5. Confirma la autorización desde tu email

### Solución 2: Configurar dominio personalizado (Recomendado para producción)

1. **Agregar dominio en Mailgun:**
   - Ve a Domains → Add New Domain
   - Ingresa tu dominio (ej: `buscafarma.com`)

2. **Configurar DNS Records:**
   Agrega estos registros DNS en tu proveedor de dominio:
   ```
   TXT: v=spf1 include:mailgun.org ~all
   TXT: k=rsa; p=[tu-clave-publica-dkim]
   CNAME: mg.buscafarma.com → mailgun.org
   MX: mxa.mailgun.org (priority 10)
   MX: mxb.mailgun.org (priority 10)
   ```

3. **Actualizar configuración:**
   ```bash
   # En .env
   MAILGUN_DOMAIN=buscafarma.com
   EMAIL_FROM=<EMAIL>
   ```

## Verificación de Estado

### Comprobar si los emails están en cola:
```bash
# Ejecutar script de prueba
bun run src/scripts/test-email.ts <EMAIL>
```

### Revisar logs de Mailgun:
1. Ve a tu dashboard de Mailgun
2. Sección "Logs"
3. Busca los emails enviados recientemente
4. Verifica el estado de entrega

## Troubleshooting

### Si los emails siguen sin llegar:

1. **Verifica spam/junk folder**
2. **Revisa logs de Mailgun** para ver el estado de entrega
3. **Confirma que la dirección está autorizada** (para sandbox)
4. **Verifica configuración DNS** (para dominio personalizado)

### Comandos útiles:

```bash
# Probar envío de email
bun run src/scripts/test-email.ts <EMAIL>

# Verificar configuración
echo "API Key: $MAILGUN_API_KEY"
echo "Domain: $MAILGUN_DOMAIN"
echo "From: $EMAIL_FROM"
```

## Próximos Pasos

1. **Inmediato:** Autorizar tu email en el dominio sandbox
2. **Corto plazo:** Configurar dominio personalizado para producción
3. **Opcional:** Configurar webhooks para tracking avanzado

## Notas Importantes

- ✅ El sistema de email está funcionando correctamente
- ✅ Mailgun está recibiendo y procesando los emails
- ⚠️ Los emails sandbox solo llegan a destinatarios autorizados
- 📧 Para producción, usa un dominio verificado personalizado
