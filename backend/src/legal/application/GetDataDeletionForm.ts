import type { DataRetentionInfo } from '../domain/DataDeletionRequest.js'
import { LegalDocument, type LegalDocumentMetadata } from '../domain/LegalDocument.js'

export class GetDataDeletionForm {
  execute(): LegalDocument {
    const metadata: LegalDocumentMetadata = {
      lastUpdated: new Date(),
      version: '1.0.0',
      language: 'es'
    }

    const content = this.generateDataDeletionFormHTML()

    return new LegalDocument('data-deletion-form', 'data-deletion', 'BuscaFarma - Solicitud de Eliminación de Cuenta', content, metadata)
  }

  /**
   * Generate a standalone HTML page for account deletion requests
   * This version includes full HTML structure for serving at root path
   */
  executeStandalone(): LegalDocument {
    const metadata: LegalDocumentMetadata = {
      lastUpdated: new Date(),
      version: '1.0.0',
      language: 'es'
    }

    const content = this.generateStandaloneDataDeletionHTML()

    return new LegalDocument('data-deletion-standalone', 'data-deletion', 'BuscaFarma - Solicitud de Eliminación de Cuenta', content, metadata)
  }

  private generateDataDeletionFormHTML(): string {
    const dataRetentionInfo: DataRetentionInfo[] = [
      {
        dataType: 'Información Personal (nombre, email)',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Datos de cuenta de usuario'
      },
      {
        dataType: 'Datos de Ubicación',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Datos de preferencias de usuario'
      },
      {
        dataType: 'Datos de Salud y Preferencias',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Información de salud del usuario'
      },
      {
        dataType: 'Medicamentos Favoritos',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Datos de preferencias de usuario'
      },
      {
        dataType: 'Tokens de Autenticación',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Tokens de seguridad'
      },
      {
        dataType: 'Registros de Auditoría',
        willBeDeleted: false,
        retentionPeriod: '7 años',
        reason: 'Cumplimiento legal y prevención de fraude'
      },
      {
        dataType: 'Registro de Solicitud de Eliminación',
        willBeDeleted: false,
        retentionPeriod: '7 años',
        reason: 'Cumplimiento con regulaciones de protección de datos'
      }
    ]

    return `
      <div class="data-deletion-form">
        <div class="app-branding">
          <h1>🏥 BuscaFarma</h1>
          <p class="app-description">Encuentra tu farmacia más cercana</p>
        </div>

        <div class="form-section">
          <h2>Solicitud de Eliminación de Cuenta</h2>
          <p class="intro-text">
            Entendemos que puedes querer eliminar tu cuenta de BuscaFarma. Esta página te permite 
            solicitar la eliminación de tu cuenta y todos los datos asociados de manera segura y transparente.
          </p>
        </div>

        <div class="form-section">
          <h3>📋 Instrucciones Paso a Paso</h3>
          <ol class="instructions-list">
            <li><strong>Completa el formulario:</strong> Proporciona tu email y confirma tu identidad</li>
            <li><strong>Revisión de datos:</strong> Revisa qué datos serán eliminados y cuáles se conservarán</li>
            <li><strong>Confirmación:</strong> Confirma tu solicitud de eliminación</li>
            <li><strong>Procesamiento:</strong> Procesaremos tu solicitud dentro de 30 días</li>
            <li><strong>Notificación:</strong> Te notificaremos cuando la eliminación esté completa</li>
          </ol>
        </div>

        <div class="form-section">
          <h3>🗂️ Información sobre Retención de Datos</h3>
          <p>La siguiente tabla muestra qué datos serán eliminados y cuáles pueden ser retenidos:</p>
          
          <div class="data-retention-table">
            <table>
              <thead>
                <tr>
                  <th>Tipo de Datos</th>
                  <th>¿Se Eliminará?</th>
                  <th>Período de Retención</th>
                  <th>Razón</th>
                </tr>
              </thead>
              <tbody>
                ${dataRetentionInfo
                  .map(
                    (info) => `
                  <tr class="${info.willBeDeleted ? 'will-delete' : 'will-retain'}">
                    <td>${info.dataType}</td>
                    <td>
                      <span class="status-badge ${info.willBeDeleted ? 'delete' : 'retain'}">
                        ${info.willBeDeleted ? '✅ Sí' : '❌ No'}
                      </span>
                    </td>
                    <td>${info.retentionPeriod}</td>
                    <td>${info.reason}</td>
                  </tr>
                `
                  )
                  .join('')}
              </tbody>
            </table>
          </div>
        </div>

        <div class="form-section">
          <h3>📝 Formulario de Solicitud</h3>
          <form id="deletionRequestForm" class="deletion-form">
            <div class="form-group">
              <label for="email">Email de la Cuenta:</label>
              <input 
                type="email" 
                id="email" 
                name="email" 
                required 
                placeholder="<EMAIL>"
                class="form-input"
              >
              <small class="form-help">Ingresa el email asociado a tu cuenta de BuscaFarma</small>
            </div>

            <div class="form-group">
              <label for="reason">Razón de Eliminación (Opcional):</label>
              <select id="reason" name="reason" class="form-input">
                <option value="">Selecciona una razón (opcional)</option>
                <option value="no-longer-needed">Ya no necesito la aplicación</option>
                <option value="privacy-concerns">Preocupaciones de privacidad</option>
                <option value="switching-apps">Cambio a otra aplicación</option>
                <option value="account-issues">Problemas con la cuenta</option>
                <option value="other">Otra razón</option>
              </select>
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input type="checkbox" id="confirmDeletion" name="confirmDeletion" required>
                <span class="checkmark"></span>
                Confirmo que entiendo que esta acción eliminará permanentemente mi cuenta y datos asociados, 
                y que algunos datos pueden ser retenidos por razones legales como se describe arriba.
              </label>
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input type="checkbox" id="confirmIdentity" name="confirmIdentity" required>
                <span class="checkmark"></span>
                Confirmo que soy el propietario legítimo de esta cuenta y tengo autorización para solicitar su eliminación.
              </label>
            </div>

            <div class="form-actions">
              <button type="submit" class="btn-primary" id="submitBtn">
                🗑️ Solicitar Eliminación de Cuenta
              </button>
            </div>
          </form>
        </div>

        <div class="form-section">
          <h3>📞 Soporte y Contacto</h3>
          <div class="contact-info">
            <p><strong>¿Necesitas ayuda con tu solicitud de eliminación?</strong></p>
            <ul>
              <li>📧 Email: <EMAIL></li>
              <li>⏰ Tiempo de respuesta: 24-48 horas</li>
              <li>🕒 Tiempo de procesamiento: Hasta 30 días</li>
            </ul>
            <p class="note">
              Si tienes problemas para acceder a tu cuenta o no puedes completar este formulario, 
              contacta nuestro equipo de soporte directamente.
            </p>
          </div>
        </div>

        <div class="form-section">
          <h3>⚖️ Información Legal</h3>
          <div class="legal-info">
            <p>
              Esta solicitud de eliminación se procesa de acuerdo con las regulaciones de protección de datos 
              aplicables, incluyendo GDPR y las políticas de Google Play Store. Algunos datos pueden ser 
              retenidos por períodos específicos para cumplir con obligaciones legales, prevención de fraude, 
              y seguridad.
            </p>
            <p>
              Para más información sobre cómo manejamos tus datos, consulta nuestra 
              <a href="/privacy-policy.html" target="_blank">Política de Privacidad</a>.
            </p>
          </div>
        </div>
      </div>

      <style>
        .data-deletion-form {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          line-height: 1.6;
          color: #333;
        }

        .app-branding {
          text-align: center;
          margin-bottom: 40px;
          padding: 30px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border-radius: 12px;
        }

        .app-branding h1 {
          margin: 0 0 10px 0;
          font-size: 2.5em;
          font-weight: bold;
        }

        .app-description {
          margin: 0;
          font-size: 1.2em;
          opacity: 0.9;
        }

        .form-section {
          margin-bottom: 40px;
          padding: 25px;
          background: white;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .form-section h2 {
          color: #2c3e50;
          border-bottom: 3px solid #3498db;
          padding-bottom: 10px;
          margin-top: 0;
        }

        .form-section h3 {
          color: #34495e;
          margin-top: 0;
        }

        .instructions-list {
          padding-left: 20px;
        }

        .instructions-list li {
          margin-bottom: 10px;
        }

        .data-retention-table {
          overflow-x: auto;
          margin: 20px 0;
        }

        .data-retention-table table {
          width: 100%;
          border-collapse: collapse;
          margin: 20px 0;
        }

        .data-retention-table th,
        .data-retention-table td {
          padding: 12px;
          text-align: left;
          border-bottom: 1px solid #ddd;
        }

        .data-retention-table th {
          background-color: #f8f9fa;
          font-weight: bold;
          color: #2c3e50;
        }

        .will-delete {
          background-color: #f8fff8;
        }

        .will-retain {
          background-color: #fff8f8;
        }

        .status-badge {
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 0.9em;
          font-weight: bold;
        }

        .status-badge.delete {
          background-color: #d4edda;
          color: #155724;
        }

        .status-badge.retain {
          background-color: #f8d7da;
          color: #721c24;
        }

        .deletion-form {
          max-width: 600px;
        }

        .form-group {
          margin-bottom: 25px;
        }

        .form-group label {
          display: block;
          margin-bottom: 8px;
          font-weight: bold;
          color: #2c3e50;
        }

        .form-input {
          width: 100%;
          padding: 12px;
          border: 2px solid #ddd;
          border-radius: 6px;
          font-size: 16px;
          transition: border-color 0.3s;
        }

        .form-input:focus {
          outline: none;
          border-color: #3498db;
        }

        .form-help {
          display: block;
          margin-top: 5px;
          color: #666;
          font-size: 0.9em;
        }

        .checkbox-label {
          display: flex;
          align-items: flex-start;
          cursor: pointer;
          font-weight: normal;
        }

        .checkbox-label input[type="checkbox"] {
          margin-right: 10px;
          margin-top: 2px;
        }

        .form-actions {
          text-align: center;
          margin-top: 30px;
        }

        .btn-primary {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border: none;
          padding: 15px 30px;
          font-size: 16px;
          font-weight: bold;
          border-radius: 8px;
          cursor: pointer;
          transition: transform 0.2s, box-shadow 0.2s;
        }

        .btn-primary:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .contact-info ul {
          list-style: none;
          padding: 0;
        }

        .contact-info li {
          margin-bottom: 8px;
          padding: 8px;
          background: #f8f9fa;
          border-radius: 4px;
        }

        .note {
          background: #e3f2fd;
          padding: 15px;
          border-radius: 6px;
          border-left: 4px solid #2196f3;
          margin-top: 15px;
        }

        .legal-info {
          background: #f8f9fa;
          padding: 20px;
          border-radius: 6px;
          border-left: 4px solid #6c757d;
        }

        .legal-info a {
          color: #3498db;
          text-decoration: none;
        }

        .legal-info a:hover {
          text-decoration: underline;
        }

        @media (max-width: 768px) {
          .data-deletion-form {
            padding: 10px;
          }
          
          .form-section {
            padding: 15px;
          }
          
          .data-retention-table {
            font-size: 0.9em;
          }
        }
      </style>

      <script>
        document.getElementById('deletionRequestForm').addEventListener('submit', async function(e) {
          e.preventDefault();
          
          const submitBtn = document.getElementById('submitBtn');
          const originalText = submitBtn.textContent;
          
          submitBtn.disabled = true;
          submitBtn.textContent = '⏳ Procesando...';
          
          const formData = new FormData(this);
          const data = {
            email: formData.get('email'),
            reason: formData.get('reason') || undefined,
            confirmDeletion: formData.get('confirmDeletion') === 'on',
            confirmIdentity: formData.get('confirmIdentity') === 'on'
          };
          
          try {
            const response = await fetch('/data-deletion/request', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (response.ok) {
              alert('✅ Solicitud enviada exitosamente. Recibirás un email de confirmación con los detalles.');
              this.reset();
            } else {
              throw new ValidationError(result.message || 'No se pudo procesar la solicitud');
            }
          } catch (error) {
            alert('❌ Error: ' + (error.message || 'Error de conexión. Por favor intenta nuevamente.'));
          } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
          }
        });
      </script>
    `
  }

  /**
   * Generate a complete standalone HTML page for account deletion requests
   * This includes full HTML structure with DOCTYPE, head, and body tags
   */
  private generateStandaloneDataDeletionHTML(): string {
    const dataRetentionInfo: DataRetentionInfo[] = [
      {
        dataType: 'Información Personal (nombre, email)',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Datos de cuenta de usuario'
      },
      {
        dataType: 'Datos de Ubicación',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Datos de preferencias de usuario'
      },
      {
        dataType: 'Datos de Salud y Preferencias',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Información de salud del usuario'
      },
      {
        dataType: 'Medicamentos Favoritos',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Datos de preferencias de usuario'
      },
      {
        dataType: 'Tokens de Autenticación',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Tokens de seguridad'
      },
      {
        dataType: 'Registros de Auditoría',
        willBeDeleted: false,
        retentionPeriod: '7 años',
        reason: 'Cumplimiento legal y prevención de fraude'
      },
      {
        dataType: 'Registro de Solicitud de Eliminación',
        willBeDeleted: false,
        retentionPeriod: '7 años',
        reason: 'Cumplimiento con regulaciones de protección de datos'
      }
    ]

    return `<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Solicitud de eliminación de cuenta de BuscaFarma - Encuentra tu farmacia más cercana">
  <meta name="keywords" content="BuscaFarma, eliminación cuenta, privacidad, datos personales">
  <meta name="author" content="BuscaFarma">
  <title>BuscaFarma - Solicitud de Eliminación de Cuenta</title>

  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- Open Graph Meta Tags -->
  <meta property="og:title" content="BuscaFarma - Solicitud de Eliminación de Cuenta">
  <meta property="og:description" content="Solicita la eliminación de tu cuenta de BuscaFarma de manera segura y transparente">
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://api.buscafarma.com/">

  <!-- Styles -->
  <style>
    :root {
      --primary-color: #667eea;
      --primary-dark: #5a67d8;
      --secondary-color: #764ba2;
      --accent-color: #4299e1;
      --success-color: #48bb78;
      --error-color: #f56565;
      --warning-color: #ed8936;
      --text-primary: #2d3748;
      --text-secondary: #4a5568;
      --text-muted: #718096;
      --bg-primary: #ffffff;
      --bg-secondary: #f7fafc;
      --bg-accent: #edf2f7;
      --border-color: #e2e8f0;
      --border-focus: #4299e1;
      --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      --radius-sm: 0.375rem;
      --radius-md: 0.5rem;
      --radius-lg: 0.75rem;
      --radius-xl: 1rem;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
      line-height: 1.6;
      color: var(--text-primary);
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    .container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 2rem;
      position: relative;
    }

    .header {
      text-align: center;
      margin-bottom: 3rem;
      padding: 4rem 2rem;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: var(--text-primary);
      border-radius: var(--radius-xl);
      box-shadow: var(--shadow-xl);
      position: relative;
      overflow: hidden;
    }

    .header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
      z-index: -1;
    }

    .header .icon {
      font-size: 4rem;
      margin-bottom: 1rem;
      display: inline-block;
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }

    .header h1 {
      font-size: 3.5rem;
      margin-bottom: 1rem;
      font-weight: 700;
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      letter-spacing: -0.02em;
    }

    .header .subtitle {
      font-size: 1.5rem;
      color: var(--text-secondary);
      margin-bottom: 1.5rem;
      font-weight: 500;
    }

    .header .description {
      font-size: 1.125rem;
      color: var(--text-muted);
      max-width: 700px;
      margin: 0 auto;
      line-height: 1.7;
    }

    .content-section {
      background: var(--bg-primary);
      margin-bottom: 2rem;
      padding: 2.5rem;
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-lg);
      border: 1px solid var(--border-color);
      transition: all 0.3s ease;
      position: relative;
    }

    .content-section:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-xl);
    }

    .content-section h2 {
      color: var(--text-primary);
      border-bottom: 3px solid var(--accent-color);
      padding-bottom: 1rem;
      margin-bottom: 2rem;
      font-size: 2rem;
      font-weight: 600;
      letter-spacing: -0.01em;
    }

    .content-section h3 {
      color: var(--text-primary);
      margin: 2rem 0 1rem 0;
      font-size: 1.5rem;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 0.75rem;
    }

    .intro-text {
      font-size: 1.125rem;
      color: var(--text-secondary);
      margin-bottom: 2rem;
      line-height: 1.8;
    }

    .instructions-list {
      padding-left: 0;
      margin: 2rem 0;
      list-style: none;
      counter-reset: step-counter;
    }

    .instructions-list li {
      margin-bottom: 1.5rem;
      font-size: 1.125rem;
      line-height: 1.7;
      padding: 1.5rem;
      background: var(--bg-secondary);
      border-radius: var(--radius-md);
      border-left: 4px solid var(--accent-color);
      position: relative;
      counter-increment: step-counter;
      transition: all 0.3s ease;
    }

    .instructions-list li:hover {
      background: var(--bg-accent);
      transform: translateX(4px);
    }

    .instructions-list li::before {
      content: counter(step-counter);
      position: absolute;
      left: -2rem;
      top: 50%;
      transform: translateY(-50%);
      background: var(--accent-color);
      color: white;
      width: 2rem;
      height: 2rem;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 0.875rem;
    }

    .instructions-list strong {
      color: var(--text-primary);
      font-weight: 600;
    }

    .data-retention-table {
      overflow-x: auto;
      margin: 2rem 0;
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-md);
      border: 1px solid var(--border-color);
    }

    .data-retention-table table {
      width: 100%;
      border-collapse: collapse;
      background: var(--bg-primary);
    }

    .data-retention-table th,
    .data-retention-table td {
      padding: 1.25rem 1rem;
      text-align: left;
      border-bottom: 1px solid var(--border-color);
      vertical-align: middle;
    }

    .data-retention-table th {
      background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-accent) 100%);
      font-weight: 600;
      color: var(--text-primary);
      font-size: 1rem;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    .data-retention-table tbody tr {
      transition: all 0.2s ease;
    }

    .data-retention-table tbody tr:hover {
      background-color: var(--bg-secondary);
    }

    .will-delete {
      background-color: rgba(72, 187, 120, 0.05);
      border-left: 4px solid var(--success-color);
    }

    .will-retain {
      background-color: rgba(245, 101, 101, 0.05);
      border-left: 4px solid var(--error-color);
    }

    .status-badge {
      padding: 0.5rem 1rem;
      border-radius: 9999px;
      font-size: 0.875rem;
      font-weight: 600;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      transition: all 0.2s ease;
    }

    .status-badge.delete {
      background-color: rgba(72, 187, 120, 0.1);
      color: var(--success-color);
      border: 1px solid rgba(72, 187, 120, 0.2);
    }

    .status-badge.retain {
      background-color: rgba(245, 101, 101, 0.1);
      color: var(--error-color);
      border: 1px solid rgba(245, 101, 101, 0.2);
    }

    .deletion-form {
      max-width: 700px;
      margin: 0 auto;
    }

    .form-group {
      margin-bottom: 2rem;
    }

    .form-group label {
      display: block;
      margin-bottom: 0.75rem;
      font-weight: 600;
      color: var(--text-primary);
      font-size: 1.125rem;
    }

    .form-input {
      width: 100%;
      padding: 1.25rem;
      border: 2px solid var(--border-color);
      border-radius: var(--radius-md);
      font-size: 1rem;
      transition: all 0.3s ease;
      background: var(--bg-primary);
      font-family: inherit;
    }

    .form-input:focus {
      outline: none;
      border-color: var(--border-focus);
      background: var(--bg-primary);
      box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
      transform: translateY(-1px);
    }

    .form-input:hover {
      border-color: var(--accent-color);
    }

    .form-help {
      display: block;
      margin-top: 0.75rem;
      color: var(--text-muted);
      font-size: 0.875rem;
      line-height: 1.5;
    }

    .checkbox-label {
      display: flex;
      align-items: flex-start;
      cursor: pointer;
      font-weight: normal;
      line-height: 1.7;
      padding: 1.5rem;
      background: var(--bg-secondary);
      border-radius: var(--radius-md);
      border: 2px solid var(--border-color);
      transition: all 0.3s ease;
      position: relative;
    }

    .checkbox-label:hover {
      background: var(--bg-accent);
      border-color: var(--accent-color);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }

    .checkbox-label input[type="checkbox"] {
      margin-right: 1rem;
      margin-top: 0.125rem;
      width: 1.25rem;
      height: 1.25rem;
      accent-color: var(--accent-color);
    }

    .form-actions {
      text-align: center;
      margin-top: 3rem;
    }

    .btn-primary {
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
      color: white;
      border: none;
      padding: 1.25rem 3rem;
      font-size: 1.125rem;
      font-weight: 600;
      border-radius: 9999px;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: var(--shadow-lg);
      text-transform: none;
      letter-spacing: 0.025em;
      font-family: inherit;
      position: relative;
      overflow: hidden;
    }

    .btn-primary::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    .btn-primary:hover::before {
      left: 100%;
    }

    .btn-primary:hover {
      transform: translateY(-3px);
      box-shadow: var(--shadow-xl);
    }

    .btn-primary:active {
      transform: translateY(-1px);
    }

    .btn-primary:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: var(--shadow-md);
    }

    .contact-info {
      background: linear-gradient(135deg, rgba(66, 153, 225, 0.1) 0%, rgba(66, 153, 225, 0.05) 100%);
      padding: 2rem;
      border-radius: var(--radius-lg);
      border-left: 4px solid var(--accent-color);
      border: 1px solid rgba(66, 153, 225, 0.2);
    }

    .contact-info ul {
      list-style: none;
      padding: 0;
      margin: 1.5rem 0;
      display: grid;
      gap: 1rem;
    }

    .contact-info li {
      padding: 1rem 1.5rem;
      background: var(--bg-primary);
      border-radius: var(--radius-md);
      box-shadow: var(--shadow-sm);
      font-weight: 500;
      transition: all 0.2s ease;
      border: 1px solid var(--border-color);
    }

    .contact-info li:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }

    .note {
      background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(72, 187, 120, 0.05) 100%);
      padding: 1.5rem;
      border-radius: var(--radius-lg);
      border-left: 4px solid var(--success-color);
      margin-top: 1.5rem;
      border: 1px solid rgba(72, 187, 120, 0.2);
      line-height: 1.7;
    }

    .legal-info {
      background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-accent) 100%);
      padding: 2rem;
      border-radius: var(--radius-lg);
      border-left: 4px solid var(--text-muted);
      line-height: 1.8;
      border: 1px solid var(--border-color);
    }

    .legal-info a {
      color: var(--accent-color);
      text-decoration: none;
      font-weight: 600;
      transition: all 0.2s ease;
    }

    .legal-info a:hover {
      text-decoration: underline;
      color: var(--primary-color);
    }

    .footer {
      text-align: center;
      margin-top: 4rem;
      padding: 3rem;
      background: linear-gradient(135deg, var(--text-primary) 0%, var(--text-secondary) 100%);
      color: white;
      border-radius: var(--radius-xl);
      box-shadow: var(--shadow-lg);
    }

    .footer p {
      margin-bottom: 0.75rem;
    }

    .footer .copyright {
      font-size: 0.875rem;
      opacity: 0.8;
    }

    /* Loading state */
    .loading {
      opacity: 0.7;
      pointer-events: none;
      position: relative;
    }

    .loading::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(2px);
      border-radius: var(--radius-lg);
      z-index: 10;
    }

    /* Success message */
    .success-message {
      background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(72, 187, 120, 0.05) 100%);
      color: var(--success-color);
      padding: 1.5rem;
      border-radius: var(--radius-lg);
      border: 1px solid rgba(72, 187, 120, 0.2);
      margin-top: 2rem;
      display: none;
      animation: slideIn 0.3s ease-out;
      box-shadow: var(--shadow-md);
    }

    /* Error message */
    .error-message {
      background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(245, 101, 101, 0.05) 100%);
      color: var(--error-color);
      padding: 1.5rem;
      border-radius: var(--radius-lg);
      border: 1px solid rgba(245, 101, 101, 0.2);
      margin-top: 2rem;
      display: none;
      animation: slideIn 0.3s ease-out;
      box-shadow: var(--shadow-md);
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateY(-10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
      .container {
        padding: 1.5rem;
      }

      .instructions-list li::before {
        left: -1.5rem;
        width: 1.5rem;
        height: 1.5rem;
        font-size: 0.75rem;
      }
    }

    @media (max-width: 768px) {
      .container {
        padding: 1rem;
      }

      .header {
        padding: 3rem 1.5rem;
        margin-bottom: 2rem;
      }

      .header h1 {
        font-size: 2.5rem;
      }

      .header .subtitle {
        font-size: 1.25rem;
      }

      .content-section {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
      }

      .content-section h2 {
        font-size: 1.75rem;
      }

      .content-section h3 {
        font-size: 1.25rem;
      }

      .instructions-list {
        padding-left: 1rem;
      }

      .instructions-list li::before {
        left: -1rem;
      }

      .data-retention-table {
        font-size: 0.875rem;
      }

      .form-input {
        padding: 1rem;
      }

      .btn-primary {
        padding: 1rem 2rem;
        font-size: 1rem;
      }
    }

    @media (max-width: 480px) {
      .container {
        padding: 0.75rem;
      }

      .header {
        padding: 2rem 1rem;
      }

      .header h1 {
        font-size: 2rem;
      }

      .header .subtitle {
        font-size: 1.125rem;
      }

      .content-section {
        padding: 1rem;
      }

      .content-section h2 {
        font-size: 1.5rem;
      }

      .instructions-list {
        padding-left: 0;
      }

      .instructions-list li {
        padding: 1rem;
        margin-left: 0;
      }

      .instructions-list li::before {
        position: relative;
        left: 0;
        margin-right: 0.75rem;
        margin-bottom: 0.5rem;
        width: 1.5rem;
        height: 1.5rem;
      }

      .data-retention-table th,
      .data-retention-table td {
        padding: 0.75rem 0.5rem;
        font-size: 0.75rem;
      }

      .checkbox-label {
        padding: 1rem;
      }

      .btn-primary {
        padding: 1rem 1.5rem;
        font-size: 0.875rem;
        width: 100%;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Header Section -->
    <div class="header">
      <div class="icon">🏥</div>
      <h1>BuscaFarma</h1>
      <p class="subtitle">Encuentra tu farmacia más cercana</p>
      <p class="description">
        Solicitud de eliminación de cuenta y datos personales de forma segura y transparente
      </p>
    </div>

    <!-- Introduction Section -->
    <div class="content-section">
      <h2>📋 Solicitud de Eliminación de Cuenta</h2>
      <p class="intro-text">
        Entendemos que puedes querer eliminar tu cuenta de BuscaFarma. Esta página te permite
        solicitar la eliminación de tu cuenta y todos los datos asociados de manera segura y transparente,
        cumpliendo con las regulaciones de protección de datos y los requisitos de Google Play Store.
      </p>
      <p class="intro-text">
        <strong>Importante:</strong> Esta acción es irreversible. Una vez procesada tu solicitud,
        no podremos recuperar tu cuenta ni tus datos personales.
      </p>
    </div>

    <!-- Instructions Section -->
    <div class="content-section">
      <h3>📝 Instrucciones Paso a Paso</h3>
      <ol class="instructions-list">
        <li><strong>Completa el formulario:</strong> Proporciona tu email y confirma tu identidad</li>
        <li><strong>Revisión de datos:</strong> Revisa qué datos serán eliminados y cuáles se conservarán</li>
        <li><strong>Confirmación:</strong> Confirma tu solicitud de eliminación</li>
        <li><strong>Procesamiento:</strong> Procesaremos tu solicitud dentro de 30 días</li>
        <li><strong>Notificación:</strong> Te notificaremos cuando la eliminación esté completa</li>
      </ol>
    </div>

    <!-- Data Retention Information -->
    <div class="content-section">
      <h3>🗂️ Información sobre Retención de Datos</h3>
      <p>La siguiente tabla muestra qué datos serán eliminados y cuáles pueden ser retenidos:</p>

      <div class="data-retention-table">
        <table>
          <thead>
            <tr>
              <th>Tipo de Datos</th>
              <th>¿Se Eliminará?</th>
              <th>Período de Retención</th>
              <th>Razón</th>
            </tr>
          </thead>
          <tbody>
            ${dataRetentionInfo
              .map(
                (info) => `
              <tr class="${info.willBeDeleted ? 'will-delete' : 'will-retain'}">
                <td><strong>${info.dataType}</strong></td>
                <td>
                  <span class="status-badge ${info.willBeDeleted ? 'delete' : 'retain'}">
                    ${info.willBeDeleted ? '✅ Sí' : '❌ No'}
                  </span>
                </td>
                <td>${info.retentionPeriod}</td>
                <td>${info.reason}</td>
              </tr>
            `
              )
              .join('')}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Deletion Request Form -->
    <div class="content-section">
      <h3>📝 Formulario de Solicitud</h3>
      <form id="deletionRequestForm" class="deletion-form">
        <div class="form-group">
          <label for="email">Email de la Cuenta:</label>
          <input
            type="email"
            id="email"
            name="email"
            required
            placeholder="<EMAIL>"
            class="form-input"
          >
          <small class="form-help">Ingresa el email asociado a tu cuenta de BuscaFarma</small>
        </div>

        <div class="form-group">
          <label for="reason">Razón de Eliminación (Opcional):</label>
          <select id="reason" name="reason" class="form-input">
            <option value="">Selecciona una razón (opcional)</option>
            <option value="no-longer-needed">Ya no necesito la aplicación</option>
            <option value="privacy-concerns">Preocupaciones de privacidad</option>
            <option value="switching-apps">Cambio a otra aplicación</option>
            <option value="account-issues">Problemas con la cuenta</option>
            <option value="other">Otra razón</option>
          </select>
        </div>

        <div class="form-group">
          <label class="checkbox-label">
            <input type="checkbox" id="confirmDeletion" name="confirmDeletion" required>
            Confirmo que entiendo que esta acción eliminará permanentemente mi cuenta y datos asociados,
            y que algunos datos pueden ser retenidos por razones legales como se describe arriba.
          </label>
        </div>

        <div class="form-group">
          <label class="checkbox-label">
            <input type="checkbox" id="confirmIdentity" name="confirmIdentity" required>
            Confirmo que soy el propietario legítimo de esta cuenta y tengo autorización para solicitar su eliminación.
          </label>
        </div>

        <div class="form-actions">
          <button type="submit" class="btn-primary" id="submitBtn">
            🗑️ Solicitar Eliminación de Cuenta
          </button>
        </div>

        <!-- Success Message -->
        <div id="successMessage" class="success-message">
          <strong>✅ Solicitud enviada exitosamente!</strong><br>
          Recibirás un email de confirmación con los detalles de tu solicitud.
        </div>

        <!-- Error Message -->
        <div id="errorMessage" class="error-message">
          <strong>❌ Error al procesar la solicitud</strong><br>
          <span id="errorText">Por favor intenta nuevamente.</span>
        </div>
      </form>
    </div>

    <!-- Support and Contact -->
    <div class="content-section">
      <h3>📞 Soporte y Contacto</h3>
      <div class="contact-info">
        <p><strong>¿Necesitas ayuda con tu solicitud de eliminación?</strong></p>
        <ul>
          <li>📧 Email: <EMAIL></li>
          <li>⏰ Tiempo de respuesta: 24-48 horas</li>
          <li>🕒 Tiempo de procesamiento: Hasta 30 días</li>
          <li>🌐 Sitio web: www.buscafarma.com</li>
        </ul>
        <p class="note">
          Si tienes problemas para acceder a tu cuenta o no puedes completar este formulario,
          contacta nuestro equipo de soporte directamente.
        </p>
      </div>
    </div>

    <!-- Legal Information -->
    <div class="content-section">
      <h3>⚖️ Información Legal</h3>
      <div class="legal-info">
        <p>
          Esta solicitud de eliminación se procesa de acuerdo con las regulaciones de protección de datos
          aplicables, incluyendo GDPR y las políticas de Google Play Store. Algunos datos pueden ser
          retenidos por períodos específicos para cumplir con obligaciones legales, prevención de fraude,
          y seguridad.
        </p>
        <p>
          Para más información sobre cómo manejamos tus datos, consulta nuestra
          <a href="/privacy-policy.html" target="_blank">Política de Privacidad</a>.
        </p>
        <p>
          <strong>BuscaFarma</strong> es una aplicación móvil que te ayuda a encontrar farmacias cercanas
          y gestionar tus medicamentos favoritos. No estamos afiliados con entidades gubernamentales.
        </p>
      </div>
    </div>

    <!-- Footer -->
    <div class="footer">
      <p><strong>🏥 BuscaFarma</strong></p>
      <p>Encuentra tu farmacia más cercana</p>
      <p class="copyright">© 2024 BuscaFarma. Todos los derechos reservados.</p>
    </div>
  </div>

  <!-- JavaScript -->
  <script>
    // Add smooth entrance animations
    document.addEventListener('DOMContentLoaded', function() {
      const sections = document.querySelectorAll('.content-section');
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
          }
        });
      }, { threshold: 0.1 });

      sections.forEach(section => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(20px)';
        section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(section);
      });
    });

    document.getElementById('deletionRequestForm').addEventListener('submit', async function(e) {
      e.preventDefault();

      const submitBtn = document.getElementById('submitBtn');
      const successMessage = document.getElementById('successMessage');
      const errorMessage = document.getElementById('errorMessage');
      const errorText = document.getElementById('errorText');
      const form = this;

      // Reset messages
      successMessage.style.display = 'none';
      errorMessage.style.display = 'none';

      // Show loading state
      const originalText = submitBtn.textContent;
      submitBtn.disabled = true;
      submitBtn.textContent = '⏳ Procesando...';
      form.classList.add('loading');

      const formData = new FormData(this);
      const data = {
        email: formData.get('email'),
        reason: formData.get('reason') || undefined,
        confirmDeletion: formData.get('confirmDeletion') === 'on',
        confirmIdentity: formData.get('confirmIdentity') === 'on'
      };

      try {
        const response = await fetch('/data-deletion/request', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data)
        });

        const result = await response.json();

        if (response.ok) {
          successMessage.style.display = 'block';
          form.reset();

          // Scroll to success message
          successMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
        } else {
          throw new ValidationError(result.message || 'Error desconocido');
        }
      } catch (error) {
        errorText.textContent = error.message || 'Error de conexión. Por favor intenta nuevamente.';
        errorMessage.style.display = 'block';

        // Scroll to error message
        errorMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
      } finally {
        // Reset loading state
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
        form.classList.remove('loading');
      }
    });

    // Add smooth scrolling for internal links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({ behavior: 'smooth' });
        }
      });
    });

    // Enhanced form validation feedback
    const emailInput = document.getElementById('email');
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');

    // Email validation with visual feedback
    emailInput.addEventListener('blur', function() {
      if (this.value && !this.checkValidity()) {
        this.style.borderColor = 'var(--error-color)';
        this.style.boxShadow = '0 0 0 3px rgba(245, 101, 101, 0.1)';
      } else if (this.value) {
        this.style.borderColor = 'var(--success-color)';
        this.style.boxShadow = '0 0 0 3px rgba(72, 187, 120, 0.1)';
      } else {
        this.style.borderColor = 'var(--border-color)';
        this.style.boxShadow = 'none';
      }
    });

    emailInput.addEventListener('input', function() {
      if (this.checkValidity() && this.value) {
        this.style.borderColor = 'var(--success-color)';
        this.style.boxShadow = '0 0 0 3px rgba(72, 187, 120, 0.1)';
      } else {
        this.style.borderColor = 'var(--border-color)';
        this.style.boxShadow = 'none';
      }
    });

    // Checkbox validation feedback
    checkboxes.forEach(checkbox => {
      checkbox.addEventListener('change', function() {
        const label = this.closest('.checkbox-label');
        if (this.checked) {
          label.style.borderColor = 'var(--success-color)';
          label.style.backgroundColor = 'rgba(72, 187, 120, 0.05)';
        } else {
          label.style.borderColor = 'var(--border-color)';
          label.style.backgroundColor = 'var(--bg-secondary)';
        }
      });
    });

    // Add floating label effect
    const inputs = document.querySelectorAll('.form-input');
    inputs.forEach(input => {
      input.addEventListener('focus', function() {
        this.parentElement.classList.add('focused');
      });

      input.addEventListener('blur', function() {
        if (!this.value) {
          this.parentElement.classList.remove('focused');
        }
      });
    });
  </script>
</body>
</html>`
  }
}
