import Mailgun from 'mailgun.js'
import formData from 'form-data'

/**
 * Email configuration interface for Mailgun API
 *
 * Mailgun API Configuration Requirements:
 * - API Key: Your Mailgun API key (starts with 'key-')
 * - Domain: Your verified Mailgun domain
 * - Region: Mailgun region ('us' or 'eu')
 * - From Address: Verified sender email address
 *
 * To set up Mailgun:
 * 1. Create a Mailgun account at https://www.mailgun.com/
 * 2. Add and verify your domain
 * 3. Get your API key from the dashboard
 * 4. Configure your DNS records for domain verification
 *
 * Required Environment Variables:
 * - MAILGUN_API_KEY=key-your-api-key-here
 * - MAILGUN_DOMAIN=your-domain.com
 * - MAILGUN_REGION=us (or 'eu' for European region)
 * - EMAIL_FROM=<EMAIL>
 */
export interface EmailConfig {
  apiKey: string
  domain: string
  region: 'us' | 'eu'
  from: string
  // Force email sending even in development mode
  forceEmailSending?: boolean
}

export interface EmailTemplate {
  subject: string
  html: string
  text: string
}

export class EmailService {
  private config: EmailConfig
  private mailgun: any

  constructor(config: EmailConfig) {
    this.config = config

    // Initialize Mailgun client
    const mailgun = new Mailgun(formData)
    this.mailgun = mailgun.client({
      username: 'api',
      key: config.apiKey,
      url: config.region === 'eu' ? 'https://api.eu.mailgun.net' : 'https://api.mailgun.net'
    })
  }

  /**
   * Verify the email configuration and connection to Mailgun
   * Call this method during application startup to ensure email is properly configured
   * Note: This is email connection verification, not JWT token verification
   */
  async verifyConnection(): Promise<boolean> {
    try {
      // Test Mailgun connection by validating the domain
      await this.mailgun.domains.get(this.config.domain)
      return true
    } catch (_error) {
      return false
    }
  }

  async sendDeletionRequestConfirmation(userEmail: string, userName: string, requestId: string): Promise<boolean> {
    const template = this.getDeletionConfirmationTemplate(userName, requestId)

    return this.sendEmail(userEmail, template)
  }

  // Note: token parameter is a secure deletion token, not a JWT token
  async sendDeletionConfirmationWithLink(userEmail: string, userName: string, requestId: string, token: string, expiresAt: Date): Promise<boolean> {
    const template = this.getDeletionConfirmationWithLinkTemplate(userName, requestId, token, expiresAt)

    return this.sendEmail(userEmail, template)
  }

  async sendDeletionCompletedNotification(userEmail: string, userName: string, requestId: string): Promise<boolean> {
    const template = this.getDeletionCompletedTemplate(userName, requestId)

    return this.sendEmail(userEmail, template)
  }

  async sendAdminDeletionNotification(adminEmail: string, userEmail: string, requestId: string): Promise<boolean> {
    const template = this.getAdminNotificationTemplate(userEmail, requestId)

    return this.sendEmail(adminEmail, template)
  }

  private async sendEmail(to: string, template: EmailTemplate): Promise<boolean> {
    try {
      // Check if we should actually send emails or just log them
      const shouldSendEmail = process.env.NODE_ENV === 'production' || this.config.forceEmailSending || process.env.FORCE_EMAIL_SENDING === 'true'

      console.log(`[EmailService] Attempting to send email to: ${to}`)
      console.log(`[EmailService] Should send email: ${shouldSendEmail}`)
      console.log(`[EmailService] NODE_ENV: ${process.env.NODE_ENV}`)
      console.log(`[EmailService] forceEmailSending: ${this.config.forceEmailSending}`)
      console.log(`[EmailService] FORCE_EMAIL_SENDING: ${process.env.FORCE_EMAIL_SENDING}`)

      if (!shouldSendEmail) {
        console.log(`[EmailService] Email sending disabled in development mode. Email would be sent to: ${to}`)
        console.log(`[EmailService] Subject: ${template.subject}`)
        return true
      }

      // Validate configuration
      if (!this.config.apiKey || !this.config.domain || !this.config.from) {
        console.error(`[EmailService] Missing required configuration:`)
        console.error(`[EmailService] - API Key: ${this.config.apiKey ? 'Set' : 'Missing'}`)
        console.error(`[EmailService] - Domain: ${this.config.domain ? 'Set' : 'Missing'}`)
        console.error(`[EmailService] - From: ${this.config.from ? 'Set' : 'Missing'}`)
        return false
      }

      // Generate unique message ID for better deliverability
      const messageId = `<${Date.now()}.${Math.random().toString(36).substring(2, 11)}@${this.config.domain}>`
      const currentDate = new Date().toUTCString()

      // Send actual email using Mailgun API with anti-spam headers
      const messageData = {
        from: this.config.from,
        to,
        subject: template.subject,
        text: template.text,
        html: template.html,
        // Anti-spam and deliverability headers
        'h:Message-ID': messageId,
        'h:Date': currentDate,
        'h:X-Mailer': 'BuscaFarma Email Service v1.0',
        'h:X-Priority': '3', // Normal priority
        'h:Importance': 'Normal',
        'h:X-Auto-Response-Suppress': 'OOF, DR, RN, NRN, AutoReply',
        'h:List-Unsubscribe': '<mailto:<EMAIL>>',
        'h:List-Unsubscribe-Post': 'List-Unsubscribe=One-Click',
        // Organization and sender identification
        'h:Organization': 'BuscaFarma',
        'h:X-Sender': 'BuscaFarma Email System',
        // Content classification
        'h:Content-Language': 'es',
        'h:X-MS-Exchange-Organization-SCL': '-1', // Bypass spam filtering hint
        // Authentication and security
        'h:X-Originating-IP': '[127.0.0.1]', // Placeholder for actual server IP
        'h:X-Spam-Status': 'No',
        'h:X-Spam-Score': '0.0'
      }

      console.log(`[EmailService] Sending email via Mailgun:`)
      console.log(`[EmailService] - From: ${messageData.from}`)
      console.log(`[EmailService] - To: ${messageData.to}`)
      console.log(`[EmailService] - Subject: ${messageData.subject}`)
      console.log(`[EmailService] - Domain: ${this.config.domain}`)

      const result = await this.mailgun.messages.create(this.config.domain, messageData)
      console.log(`[EmailService] ✅ Email sent successfully!`)
      console.log(`[EmailService] Mailgun response:`, result)
      return true
    } catch (error) {
      console.error(`[EmailService] ❌ Failed to send email:`)
      console.error(`[EmailService] Error:`, error)
      console.error(`[EmailService] Error message:`, error instanceof Error ? error.message : 'Unknown error')
      console.error(`[EmailService] Error stack:`, error instanceof Error ? error.stack : 'No stack trace')
      return false
    }
  }

  private getDeletionConfirmationTemplate(userName: string, requestId: string): EmailTemplate {
    const subject = 'BuscaFarma - Solicitud de Privacidad Recibida'

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${subject}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #2c3e50; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
          .request-id { background: #ecf0f1; padding: 10px; border-radius: 5px; font-family: monospace; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>BuscaFarma</h1>
            <h2>Solicitud de Eliminación de Cuenta</h2>
          </div>
          <div class="content">
            <p>Hola ${userName},</p>
            <p>Hemos recibido tu solicitud para eliminar tu cuenta de BuscaFarma y todos los datos asociados.</p>
            <p><strong>ID de Solicitud:</strong></p>
            <div class="request-id">${requestId}</div>
            <p><strong>¿Qué sucederá a continuación?</strong></p>
            <ul>
              <li>Procesaremos tu solicitud dentro de los próximos 30 días</li>
              <li>Eliminaremos permanentemente tu información personal, datos de salud, favoritos y preferencias</li>
              <li>Algunos datos pueden retenerse por razones legales (registros de auditoría)</li>
              <li>Te notificaremos cuando el proceso esté completo</li>
            </ul>
            <p>Si no solicitaste esta eliminación o tienes preguntas, contacta nuestro soporte inmediatamente.</p>
          </div>
          <div class="footer">
            <p><strong>BuscaFarma</strong> - Encuentra tu farmacia más cercana</p>
            <p>📧 Contacto: <EMAIL> | 📞 Soporte: +51 1 234-5678</p>
            <p>🏢 Lima, Perú | 🌐 www.buscafarma.com</p>
            <hr style="border: 1px solid #ddd; margin: 15px 0;">
            <p style="font-size: 11px; color: #888;">
              Este es un mensaje automático relacionado con tu cuenta.
              Si no deseas recibir estos correos, puedes <a href="mailto:<EMAIL>?subject=Unsubscribe">darte de baja aquí</a>.
            </p>
          </div>
        </div>
      </body>
      </html>
    `

    const text = `
BuscaFarma - Solicitud de Eliminación de Cuenta

Hola ${userName},

Hemos recibido tu solicitud para eliminar tu cuenta de BuscaFarma y todos los datos asociados.

ID de Solicitud: ${requestId}

¿Qué sucederá a continuación?
- Procesaremos tu solicitud dentro de los próximos 30 días
- Eliminaremos permanentemente tu información personal, datos de salud, favoritos y preferencias
- Algunos datos pueden retenerse por razones legales (registros de auditoría)
- Te notificaremos cuando el proceso esté completo

Si no solicitaste esta eliminación o tienes preguntas, contacta nuestro soporte inmediatamente.

BuscaFarma - Encuentra tu farmacia más cercana
Este es un mensaje automático, por favor no respondas a este correo.
    `

    return { subject, html, text }
  }

  private getDeletionCompletedTemplate(userName: string, requestId: string): EmailTemplate {
    const subject = 'BuscaFarma - Solicitud de Privacidad Completada'

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${subject}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #27ae60; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
          .request-id { background: #ecf0f1; padding: 10px; border-radius: 5px; font-family: monospace; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>BuscaFarma</h1>
            <h2>Eliminación de Cuenta Completada</h2>
          </div>
          <div class="content">
            <p>Hola ${userName},</p>
            <p>Tu solicitud de eliminación de cuenta ha sido procesada exitosamente.</p>
            <p><strong>ID de Solicitud:</strong></p>
            <div class="request-id">${requestId}</div>
            <p><strong>Datos eliminados:</strong></p>
            <ul>
              <li>Información personal (nombre, email)</li>
              <li>Datos de ubicación</li>
              <li>Datos de salud y preferencias</li>
              <li>Medicamentos favoritos</li>
              <li>Tokens de autenticación</li>
            </ul>
            <p>Gracias por haber usado BuscaFarma. Si decides volver en el futuro, serás bienvenido/a a crear una nueva cuenta.</p>
          </div>
          <div class="footer">
            <p><strong>BuscaFarma</strong> - Encuentra tu farmacia más cercana</p>
            <p>📧 Contacto: <EMAIL> | 📞 Soporte: +51 1 234-5678</p>
            <p>🏢 Lima, Perú | 🌐 www.buscafarma.com</p>
            <hr style="border: 1px solid #ddd; margin: 15px 0;">
            <p style="font-size: 11px; color: #888;">
              Este es un mensaje automático relacionado con tu cuenta.
              Si no deseas recibir estos correos, puedes <a href="mailto:<EMAIL>?subject=Unsubscribe">darte de baja aquí</a>.
            </p>
          </div>
        </div>
      </body>
      </html>
    `

    const text = `
BuscaFarma - Eliminación de Cuenta Completada

Hola ${userName},

Tu solicitud de eliminación de cuenta ha sido procesada exitosamente.

ID de Solicitud: ${requestId}

Datos eliminados:
- Información personal (nombre, email)
- Datos de ubicación
- Datos de salud y preferencias
- Medicamentos favoritos
- Tokens de autenticación

Gracias por haber usado BuscaFarma. Si decides volver en el futuro, serás bienvenido/a a crear una nueva cuenta.

BuscaFarma - Encuentra tu farmacia más cercana
    `

    return { subject, html, text }
  }

  private getDeletionConfirmationWithLinkTemplate(userName: string, requestId: string, token: string, expiresAt: Date): EmailTemplate {
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:3000'
    const confirmationUrl = `${backendUrl}/api/v1/legal/data-deletion/confirm/${token}`
    const expirationTime = expiresAt.toLocaleString('es-ES', {
      timeZone: 'America/Lima',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })

    const subject = 'BuscaFarma - Confirma tu Solicitud de Privacidad'

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${subject}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #e74c3c; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
          .request-id { background: #ecf0f1; padding: 10px; border-radius: 5px; font-family: monospace; }
          .confirm-button {
            display: inline-block;
            background: #e74c3c;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
            font-weight: bold;
          }
          .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
          }
          .expiration { color: #e74c3c; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>BuscaFarma</h1>
            <h2>⚠️ Confirma la Eliminación de tu Cuenta</h2>
          </div>
          <div class="content">
            <p>Hola ${userName},</p>
            <p>Hemos recibido tu solicitud para eliminar permanentemente tu cuenta de BuscaFarma y todos los datos asociados.</p>

            <div class="warning">
              <strong>⚠️ ADVERTENCIA:</strong> Esta acción es <strong>IRREVERSIBLE</strong>. Una vez confirmada, no podrás recuperar tu cuenta ni tus datos.
            </div>

            <p><strong>ID de Solicitud:</strong></p>
            <div class="request-id">${requestId}</div>

            <p>Para confirmar la eliminación de tu cuenta, haz clic en el siguiente botón:</p>

            <div style="text-align: center;">
              <a href="${confirmationUrl}" class="confirm-button">CONFIRMAR ELIMINACIÓN DE CUENTA</a>
            </div>

            <p>O copia y pega este enlace en tu navegador:</p>
            <p style="word-break: break-all; background: #f8f9fa; padding: 10px; border-radius: 5px;">${confirmationUrl}</p>

            <p class="expiration"><strong>⏰ Este enlace expira el ${expirationTime}</strong></p>

            <p><strong>¿Qué datos se eliminarán?</strong></p>
            <ul>
              <li>Información personal (nombre, email)</li>
              <li>Datos de ubicación y preferencias</li>
              <li>Datos de salud</li>
              <li>Medicamentos favoritos</li>
              <li>Tokens de autenticación</li>
            </ul>

            <p><strong>Si NO solicitaste esta eliminación:</strong></p>
            <ul>
              <li>NO hagas clic en el enlace</li>
              <li>Ignora este correo</li>
              <li>Contacta nuestro soporte si tienes dudas</li>
            </ul>
          </div>
          <div class="footer">
            <p><strong>BuscaFarma</strong> - Encuentra tu farmacia más cercana</p>
            <p>📧 Contacto: <EMAIL> | 📞 Soporte: +51 1 234-5678</p>
            <p>🏢 Lima, Perú | 🌐 www.buscafarma.com</p>
            <hr style="border: 1px solid #ddd; margin: 15px 0;">
            <p style="font-size: 11px; color: #888;">
              Este es un mensaje automático relacionado con tu cuenta.
              Si no deseas recibir estos correos, puedes <a href="mailto:<EMAIL>?subject=Unsubscribe">darte de baja aquí</a>.
            </p>
          </div>
        </div>
      </body>
      </html>
    `

    const text = `
BuscaFarma - Confirma la Eliminación de tu Cuenta

Hola ${userName},

Hemos recibido tu solicitud para eliminar permanentemente tu cuenta de BuscaFarma y todos los datos asociados.

⚠️ ADVERTENCIA: Esta acción es IRREVERSIBLE. Una vez confirmada, no podrás recuperar tu cuenta ni tus datos.

ID de Solicitud: ${requestId}

Para confirmar la eliminación de tu cuenta, visita el siguiente enlace:
${confirmationUrl}

⏰ Este enlace expira el ${expirationTime}

¿Qué datos se eliminarán?
- Información personal (nombre, email)
- Datos de ubicación y preferencias
- Datos de salud
- Medicamentos favoritos
- Tokens de autenticación

Si NO solicitaste esta eliminación:
- NO visites el enlace
- Ignora este correo
- Contacta nuestro soporte si tienes dudas

BuscaFarma - Encuentra tu farmacia más cercana
Este es un mensaje automático, por favor no respondas a este correo.
Soporte: <EMAIL>
    `

    return { subject, html, text }
  }

  private getAdminNotificationTemplate(userEmail: string, requestId: string): EmailTemplate {
    const subject = `BuscaFarma Admin - Nueva Solicitud de Eliminación: ${requestId}`

    const html = `
      <h2>Nueva Solicitud de Eliminación de Cuenta</h2>
      <p><strong>Usuario:</strong> ${userEmail}</p>
      <p><strong>ID de Solicitud:</strong> ${requestId}</p>
      <p><strong>Fecha:</strong> ${new Date().toLocaleString('es-ES')}</p>
      <p>Revisa el panel de administración para procesar esta solicitud.</p>
    `

    const text = `
Nueva Solicitud de Eliminación de Cuenta

Usuario: ${userEmail}
ID de Solicitud: ${requestId}
Fecha: ${new Date().toLocaleString('es-ES')}

Revisa el panel de administración para procesar esta solicitud.
    `

    return { subject, html, text }
  }
}
